--CREATE OR REPLACE VIEW ANGYPrintList AS
SELECT 
    si.studyid as pno, --P号
    si.ACCESSNUM as ano, --A号
    si.DEPARTMENTPATIENTID as deviceno, --影像号
    si.CHECKSERIALNUM as serialno, --流水号
    si.STUDYID as examno, --检查号
    '' as Proaccount, --职业号
    CASE 
        WHEN pi.HISPATIENTTYPE = 1 THEN pi.CLINICPATIENTID
        WHEN pi.HISPATIENTTYPE = 2 THEN pi.INFEEPATIENTID
        ELSE pi.ENETPATIENTID
    END as hzid, --患者编号
    pi.INFEEPATIENTID as ipno, --住院号
    '' as medcardno, --就诊卡号
    '' as cardno, --取片号
    pi.CLINICPATIENTID as clno, --门诊号
    si.DIAGID as regno, --登记编号
    CASE 
        WHEN pi.HISPATIENTTYPE = 1 THEN '门诊'
        WHEN pi.HISPATIENTTYPE = 2 THEN '住院'
        WHEN pi.HISPATIENTTYPE = 3 THEN '体检'
        WHEN si.IFEMERGENCY = 1 THEN '急诊'
        ELSE '其他'
    END as patienttype, --病人类别
    '' as tbpatient, --结核病患者
    pi.PHONENUMBER as tel, --手机号码
    pi.IDNUMBER as id, --身份证号码
    pi.PATIENTNAME as name, --患者姓名
    pi.PATIENTSPELLNAME as namepy, --姓名拼音
    pi.SEX as sex, --性别
    pi.BIRTHDAY as birthday, --出生日期
    CASE 
        WHEN si.AGEUNIT IS NOT NULL THEN CAST(si.AGE AS VARCHAR2(10)) || si.AGEUNIT
        ELSE CAST(si.AGE AS VARCHAR2(10)) || '岁'
    END as age, --年龄
    si.SICKBED as bedno, --床号
    pd.DEPARTMENTNAME as dept, --申请科室
    si.SICKROOM as Ward, --病区
    '' as Symptoms, --症状
    '' as "HP test", --HP试验
    '' as Biopsysite, --活检部位
    sp.STUDYPOSITION as checkpart, --检查部位
    '' as Checkpartcount, --检查部位收费数量
    ci.CHECKITEM as Checkmeans, --检查方法
    '' as scanseq, --扫描序列
    dt.DEVICETYPENAME as equipment, --设备类型
    dev.DEVICENAME as device, --检查仪器
    si.PREDIAGNOSE as clindiag, --临床诊断
    rpt.REPORTDESCRIBE as feature, --影像表现
    rpt.REPORTDIAGNOSE as diag, --影像诊断
    rpt.REPORTADVICE as Suggestion, --建议
    '' as Pathologicdiag, --病理诊断
    '' as Consultingdoc, --会诊医生
    si.DOCTORCODE as apydoc, --申请医生
  (SELECT USERNAME FROM PACSUSER WHERE USERID = rpt.DOCID1) as rptdoc, --报告医生
  (SELECT USERNAME FROM PACSUSER WHERE USERID = rpt.DOCID2) as auditdoc, --审核医生
    '' as apytime, --申请日期
    si.SEPERATETIME as regtime, --登记时间
    si.STUDYTIME as checktime, --检查日期
    si.PRESTARTTIME as rpttime, --报告日期
    si.SECENDTIME as audittime, --审核日期
    CASE 
        WHEN si.STUDYSTATUS = 0 THEN '登记'
        WHEN si.STUDYSTATUS = 50 THEN '检查'
        WHEN si.STUDYSTATUS = 60 THEN '已写未审核'
        WHEN si.STUDYSTATUS = 70 THEN '已写已审核'
        WHEN si.TEMPSAVESTATE = 1 THEN '暂存'
        WHEN rpt.IFREPORTPRINT = 1 THEN '已打印'
        ELSE '其他'
    END as status, --报告状态
    '' as reportpath, --PDF报告路径
    '' as Filmcharge, --胶片收费
    '' as imagefilepath, --Dicom图像路径
    si.DSTUDYUID as studyinstanceuid, --检查记录唯一编号
    si.FILENUM as Dicomcount, --DICOM图像数量
    '' as DICOMArchivingStatus --DICOM归档状态
FROM 
    STUDYINFO si
    LEFT JOIN PATIENTINFO pi ON si.CHECKSERIALNUM = pi.CHECKSERIALNUM
    LEFT JOIN PACSDEPARTMENT pd ON si.DEPARTMENTID = pd.DEPARTMENTID
    LEFT JOIN DEVICETYPEINFO dt ON si.DEVICETYPEID = dt.DEVICETYPEID
    LEFT JOIN DEVICETABLE dev ON si.DEVICEID = dev.DEVICEID
    LEFT JOIN PATIENTDIAGRPTINFO rpt ON si.DIAGRPTID = rpt.DIAGRPTID
    LEFT JOIN STUDYAPPENDPOSITION sap ON si.CHECKSERIALNUM = sap.CHECKSERIALNUM
    LEFT JOIN STUDYPOSITIONINFO sp ON sap.STUDYPOSITIONID = sp.STUDYPOSITIONID
    LEFT JOIN CHECKITEM ci ON sap.CHECKITEMID = ci.CHECKITEMID
WHERE 
    si.ISAVAILABLE = 1 --只显示未删除的记录
  --  and si.chkdeptid in (21,31,41)
    and si.studytime >sysdate-1