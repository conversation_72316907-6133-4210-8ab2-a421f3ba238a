# ANGYPrintList 视图SQL

```sql
CREATE OR REPLACE VIEW ANGYPrintList AS
SELECT 
    '' as pno, --P号
    si.ACCESSNUM as ano, --A号
    si.DEPARTMENTPATIENTID as deviceno, --影像号
    si.CHECKSERIALNUM as serialno, --流水号
    si.STUDYID as examno, --检查号
    '' as Proaccount, --职业号
    CASE 
        WHEN pi.HISPATIENTTYPE = 1 THEN pi.CLINICPATIENTID
        WHEN pi.HISPATIENTTYPE = 2 THEN pi.INFEEPATIENTID
        ELSE pi.ENETPATIENTID
    END as hzid, --患者编号
    pi.INFEEPATIENTID as ipno, --住院号
    '' as medcardno, --就诊卡号
    '' as cardno, --取片号
    pi.CLINICPATIENTID as clno, --门诊号
    si.DIAGID as regno, --登记编号
    CASE 
        WHEN pi.HISPATIENTTYPE = 1 THEN '门诊'
        WHEN pi.HISPATIENTTYPE = 2 THEN '住院'
        WHEN si.IFEMERGENCY = 1 THEN '急诊'
        ELSE '其他'
    END as patienttype, --病人类别
    '' as tbpatient, --结核病患者
    pi.PHONENUMBER as tel, --手机号码
    pi.IDNUMBER as id, --身份证号码
    pi.PATIENTNAME as name, --患者姓名
    pi.PATIENTSPELLNAME as namepy, --姓名拼音
    pi.SEX as sex, --性别
    pi.BIRTHDAY as birthday, --出生日期
    CASE 
        WHEN si.AGEUNIT IS NOT NULL THEN CAST(si.AGE AS VARCHAR2(10)) || si.AGEUNIT
        ELSE CAST(si.AGE AS VARCHAR2(10)) || '岁'
    END as age, --年龄
    si.SICKBED as bedno, --床号
    pd.DEPARTMENTNAME as dept, --申请科室
    si.SICKROOM as Ward, --病区
    '' as Symptoms, --症状
    '' as "HP test", --HP试验
    '' as Biopsysite, --活检部位
    sp.STUDYPOSITION as checkpart, --检查部位
    '' as Checkpartcount, --检查部位收费数量
    ci.CHECKITEM as Checkmeans, --检查方法
    '' as scanseq, --扫描序列
    dt.DEVICETYPENAME as equipment, --设备类型
    dev.DEVICENAME as device, --检查仪器
    si.PREDIAGNOSE as clindiag, --临床诊断
    rpt.REPORTDESCRIBE as feature, --影像表现
    rpt.REPORTDIAGNOSE as diag, --影像诊断
    rpt.REPORTADVICE as Suggestion, --建议
    '' as Pathologicdiag, --病理诊断
    '' as Consultingdoc, --会诊医生
    si.DOCTORCODE as apydoc, --申请医生
    CASE 
        WHEN rpt.DOCID1 IS NOT NULL THEN 
            (SELECT USERNAME FROM PACSUSER WHERE USERID = rpt.DOCID1)
        ELSE ''
    END as rptdoc, --报告医生
    CASE 
        WHEN rpt.DOCID2 IS NOT NULL THEN 
            (SELECT USERNAME FROM PACSUSER WHERE USERID = rpt.DOCID2)
        ELSE ''
    END as auditdoc, --审核医生
    '' as apytime, --申请日期
    si.SEPERATETIME as regtime, --登记时间
    si.STUDYTIME as checktime, --检查日期
    si.PRESTARTTIME as rpttime, --报告日期
    si.SECENDTIME as audittime, --审核日期
    CASE 
        WHEN si.STUDYSTATUS = 0 THEN '登记'
        WHEN si.STUDYSTATUS = 1 THEN '检查'
        WHEN si.STUDYSTATUS = 2 THEN '已写未审核'
        WHEN si.STUDYSTATUS = 3 THEN '已写已审核'
        WHEN si.TEMPSAVESTATE = 1 THEN '暂存'
        WHEN rpt.IFREPORTPRINT = 1 THEN '已打印'
        ELSE '其他'
    END as status, --报告状态
    '' as reportpath, --PDF报告路径
    '' as Filmcharge, --胶片收费
    '' as imagefilepath, --Dicom图像路径
    si.DSTUDYUID as studyinstanceuid, --检查记录唯一编号
    CAST(si.FILENUM AS VARCHAR2(50)) as Dicomcount, --DICOM图像数量
    '' as DICOMArchivingStatus --DICOM归档状态
FROM 
    STUDYINFO si
    LEFT JOIN PATIENTINFO pi ON si.CHECKSERIALNUM = pi.CHECKSERIALNUM
    LEFT JOIN PACSDEPARTMENT pd ON si.DEPARTMENTID = pd.DEPARTMENTID
    LEFT JOIN DEVICETYPEINFO dt ON si.DEVICETYPEID = dt.DEVICETYPEID
    LEFT JOIN DEVICETABLE dev ON si.DEVICEID = dev.DEVICEID
    LEFT JOIN PATIENTDIAGRPTINFO rpt ON si.DIAGRPTID = rpt.DIAGRPTID
    LEFT JOIN STUDYAPPENDPOSITION sap ON si.CHECKSERIALNUM = sap.CHECKSERIALNUM
    LEFT JOIN STUDYPOSITIONINFO sp ON sap.STUDYPOSITIONID = sp.STUDYPOSITIONID
    LEFT JOIN CHECKITEM ci ON sap.CHECKITEMID = ci.CHECKITEMID
WHERE 
    si.ISAVAILABLE = 1 --只显示未删除的记录
    AND sap.ISAVAILABLE = 1; --只显示可用的检查部位记录
```

## 说明

1. **字段映射关系**：
   - P号(pno)：暂时为空，需要根据业务规则确定
   - A号(ano)：使用STUDYINFO.ACCESSNUM
   - 影像号(deviceno)：使用STUDYINFO.DEPARTMENTPATIENTID
   - 流水号(serialno)：使用STUDYINFO.CHECKSERIALNUM
   - 检查号(examno)：使用STUDYINFO.STUDYID

2. **患者信息**：
   - 患者编号根据HISPATIENTTYPE判断使用门诊号还是住院号
   - 年龄字段组合了数值和单位
   - 病人类别根据HISPATIENTTYPE和IFEMERGENCY判断

3. **医生信息**：
   - 报告医生和审核医生通过关联PACSUSER表获取用户名
   - 申请医生直接使用DOCTORCODE字段

4. **状态信息**：
   - 报告状态根据多个状态字段综合判断
   - 只显示未删除的有效记录

5. **空字段**：
   - 对于表结构中没有对应字段的列，按要求设置为空字符串
   - 每个字段都添加了相应的注释说明
