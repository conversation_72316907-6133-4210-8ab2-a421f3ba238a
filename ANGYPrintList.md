# ANGYPrintList 视图SQL

```sql
CREATE OR REPLACE VIEW ANGYPrintList AS
SELECT 
    '' as pno, --P号
    s.ACCESSNUM as ano, --A号
    '' as deviceno, --影像号
    s.CHECKSERIALNUM as serialno, --流水号
    s.STUDYID as examno, --检查号
    '' as Proaccount, --职业号
    p.CLINICPATIENTID as hzid, --患者编号
    p.INFEEPATIENTID as ipno, --住院号
    '' as medcardno, --就诊卡号
    '' as cardno, --取片号
    p.CLINICPATIENTID as clno, --门诊号
    s.CHECKSERIALNUM as regno, --登记编号
    CASE 
        WHEN p.HISPATIENTTYPE = 1 THEN '门诊'
        WHEN p.HISPATIENTTYPE = 2 THEN '住院'
        WHEN p.HISPATIENTTYPE = 3 THEN '急诊'
        WHEN p.HISPATIENTTYPE = 4 THEN '体检'
        ELSE '其他'
    END as patienttype, --病人类别
    '' as tbpatient, --结核病患者
    p.PHONENUMBER as tel, --手机号码
    p.IDNUMBER as id, --身份证号码
    p.PATIENTNAME as name, --患者姓名
    p.PATIENTSPELLNAME as namepy, --姓名拼音
    p.SEX as sex, --性别
    p.BIRTHDAY as birthday, --出生日期
    CASE 
        WHEN s.AGEUNIT = '岁' THEN s.AGE || '岁'
        WHEN s.AGEUNIT = '月' THEN s.AGE || '月'
        WHEN s.AGEUNIT = '天' THEN s.AGE || '天'
        WHEN s.AGEUNIT = '小时' THEN s.AGE || '小时'
        ELSE s.AGE || s.AGEUNIT
    END as age, --年龄
    s.SICKBED as bedno, --床号
    d.DEPARTMENTNAME as dept, --申请科室
    '' as Ward, --病区
    '' as Symptoms, --症状
    '' as "HP test", --HP试验
    '' as Biopsysite, --活检部位
    s.STUDYSCRIPTION as checkpart, --检查部位
    '' as Checkpartcount, --检查部位收费数量
    '' as Checkmeans, --检查方法
    '' as scanseq, --扫描序列
    dt.DEVICETYPENAME as equipment, --设备类型
    dev.DEVICENAME as device, --检查仪器AE Title
    s.PREDIAGNOSE as clindiag, --临床诊断
    r.REPORTDESCRIBE as feature, --影像表现
    r.REPORTDIAGNOSE as diag, --影像诊断
    r.REPORTADVICE as Suggestion, --建议
    '' as Pathologicdiag, --病理诊断
    '' as Consultingdoc, --会诊医生
    s.DOCTORCODE as apydoc, --申请医生
    r.DOCID1 as rptdoc, --报告医生
    r.DOCID2 as auditdoc, --审核医生
    '' as apytime, --申请日期
    s.SEPERATETIME as regtime, --登记时间
    s.STUDYTIME as checktime, --检查日期
    r.OPERATETIME as rpttime, --报告日期
    '' as audittime, --审核日期
    CASE 
        WHEN s.STUDYSTATUS = 0 THEN '登记'
        WHEN s.STUDYSTATUS = 10 THEN '登记'
        WHEN s.STUDYSTATUS = 20 THEN '检查'
        WHEN s.STUDYSTATUS = 30 THEN '已写未审核'
        WHEN s.STUDYSTATUS = 35 THEN '临时报告'
        WHEN s.STUDYSTATUS = 40 THEN '已写已审核'
        WHEN s.STUDYSTATUS = 45 THEN '暂存'
        WHEN s.STUDYSTATUS = 50 THEN '已打印'
        WHEN s.STUDYSTATUS = 55 THEN '已审核撤回'
        WHEN s.STUDYSTATUS = 60 THEN '临时报告撤回'
        ELSE '其他'
    END as status, --报告状态
    r.FILEPATH as reportpath, --PDF报告路径
    '' as Filmcharge, --胶片收费
    '' as imagefilepath, --Dicom图像路径
    s.DSTUDYUID as studyinstanceuid, --检查记录唯一编号
    s.FILENUM as Dicomcount, --DICOM图像数量
    '' as DICOMArchivingStatus --DICOM归档状态
FROM STUDYINFO s
LEFT JOIN PATIENTINFO p ON s.CHECKSERIALNUM = p.CHECKSERIALNUM
LEFT JOIN PACSDEPARTMENT d ON s.DEPARTMENTID = d.DEPARTMENTID
LEFT JOIN DEVICETYPEINFO dt ON s.DEVICETYPEID = dt.DEVICETYPEID
LEFT JOIN DEVICETABLE dev ON s.DEVICEID = dev.DEVICEID
LEFT JOIN PATIENTDIAGRPTINFO r ON s.DIAGRPTID = r.DIAGRPTID
WHERE s.ISAVAILABLE = 1;
```

## 字段映射说明

### 主要表关联
- **STUDYINFO (s)**: 主表，包含检查基本信息
- **PATIENTINFO (p)**: 患者信息表
- **PACSDEPARTMENT (d)**: 科室信息表
- **DEVICETYPEINFO (dt)**: 设备类型信息表
- **DEVICETABLE (dev)**: 设备明细表
- **PATIENTDIAGRPTINFO (r)**: 报告信息表

### 关键字段映射
- **P号**: 暂未找到对应字段，保留为空
- **A号**: 对应STUDYINFO.ACCESSNUM
- **流水号**: 对应STUDYINFO.CHECKSERIALNUM
- **检查号**: 对应STUDYINFO.STUDYID
- **患者姓名**: 对应PATIENTINFO.PATIENTNAME
- **性别**: 对应PATIENTINFO.SEX
- **出生日期**: 对应PATIENTINFO.BIRTHDAY
- **申请科室**: 对应PACSDEPARTMENT.DEPARTMENTNAME
- **设备类型**: 对应DEVICETYPEINFO.DEVICETYPENAME
- **临床诊断**: 对应STUDYINFO.PREDIAGNOSE
- **影像表现**: 对应PATIENTDIAGRPTINFO.REPORTDESCRIBE
- **影像诊断**: 对应PATIENTDIAGRPTINFO.REPORTDIAGNOSE
- **建议**: 对应PATIENTDIAGRPTINFO.REPORTADVICE
- **申请医生**: 对应STUDYINFO.DOCTORCODE
- **报告医生**: 对应PATIENTDIAGRPTINFO.DOCID1
- **审核医生**: 对应PATIENTDIAGRPTINFO.DOCID2
- **检查时间**: 对应STUDYINFO.STUDYTIME
- **报告时间**: 对应PATIENTDIAGRPTINFO.OPERATETIME
- **DICOM图像数量**: 对应STUDYINFO.FILENUM
- **检查记录唯一编号**: 对应STUDYINFO.DSTUDYUID

### 注意事项
1. 部分字段在现有表结构中未找到对应关系，已标记为空
2. 病人类别根据PATIENTINFO.HISPATIENTTYPE进行映射
3. 报告状态根据STUDYINFO.STUDYSTATUS进行映射
4. 年龄字段结合AGE和AGEUNIT进行格式化显示
5. 视图过滤条件为ISAVAILABLE = 1，确保只显示有效数据 