# 数据库表结构

## HOSPITALINFO（医院信息表）

| 字段名            | 数据类型      | 默认值 | 描述                                  |
| ----------------- | ------------- | ------ | ------------------------------------- |
| HOSPITALID        | VARCHAR2(32)  | -      | 所属医院编号                          |
| HOSPITALNAME      | VARCHAR2(128) | -      | 医院名称                              |
| REQMARK           | NUMBER(1)     | 0      | 申请医院标记                          |
| OPERATORID        | VARCHAR2(32)  | -      | 操作员编号                            |
| OPERATETIME       | DATE          | -      | 操作时间                              |
| HOSPITALROPERTY   | NUMBER(2)     | 0      | 医院属性，0:申请医院 1:管理科室的医院 |
| HOSPITALSHORTNAME | VARCHAR2(32)  | -      | 医院名称简称                          |
| HOSPITALCODE      | NUMBER(1)     | -      | 医院代码                              |

## PACSDEPARTMENT（PACS部门表）

| 字段名             | 数据类型     | 默认值 | 描述                                                         |
| ------------------ | ------------ | ------ | ------------------------------------------------------------ |
| DEPARTMENTID       | VARCHAR2(32) | -      | 部门编号                                                     |
| HOSPITALID         | VARCHAR2(32) | -      | 所属医院编号                                                 |
| DEPARTMENTCODE     | VARCHAR2(32) | -      | 部门代码                                                     |
| DEPARTMENTNAME     | VARCHAR2(32) | -      | 部门名称                                                     |
| DEPT_DESCRIPTION   | VARCHAR2(64) | -      | 备注                                                         |
| ISAVAILABLE        | NUMBER(4)    | 1      | 是否可用，1：可用，0：不可用                                 |
| OPERATORID         | VARCHAR2(32) | -      | 操作员编号                                                   |
| OPERATETIME        | DATE         | -      | 操作时间                                                     |
| HISDEPTID          | VARCHAR2(8)  | -      | 保存HIS部门编号，可用于同步HIS部门名称                       |
| DEPARTMENTPROPERTY | NUMBER(2)    | 0      | 0：普通部门，不等于0：pacs系统工作部门 可用系统管理分配用户 分配设备，1：标准界面，2：带快速录入界面（多部位，单检查方法），3：单部位复选检查项目模式，4：其他模式 |
| DEPARTMENTAREA     | VARCHAR2(32) | -      | 部门区域                                                     |

## PACSUSER（PACS用户表）

| 字段名        | 数据类型     | 默认值  | 描述                   |
| ------------- | ------------ | ------- | ---------------------- |
| USERID        | VARCHAR2(32) | -       | 用户编号               |
| LOGINID       | VARCHAR2(16) | -       | PACS用户登录名         |
| USERCODE      | VARCHAR2(32) | -       | 用户代码               |
| USERNAME      | VARCHAR2(32) | -       | 用户名称               |
| PWD           | VARCHAR2(32) | -       | PACS用户登录密码       |
| SEX           | VARCHAR2(4)  | -       | 性别                   |
| BIRTHDAY      | DATE         | -       | 生日                   |
| DUTY          | VARCHAR2(24) | -       | 职责                   |
| TECHTITLE     | VARCHAR2(24) | -       | 职称                   |
| HOSPITALID    | NUMBER       | -       | 所属医院编号           |
| DEPARTMENTID  | VARCHAR2(32) | -       | 部门编号               |
| GROUPID       | NUMBER(3)    | -       | 工作组编号             |
| PHONENUMBER   | VARCHAR2(20) | -       | 电话号码               |
| USERLEVEL     | NUMBER(5,2)  | -       | 用户级别               |
| IFFORBIDDEN   | NUMBER(1)    | -       | 是否被禁用 1代表被禁用 |
| REMARK        | VARCHAR2(64) | -       | 备注信息               |
| DIGITALNAME   | BLOB         | -       | 数字签名               |
| OPERATORID    | VARCHAR2(32) | -       | 操作员编号             |
| OPERATETIME   | DATE         | sysdate | 操作时间               |
| DEFAULTROLEID | VARCHAR2(3)  | -       | 默认角色编号           |

## DEVICETYPEINFO（设备类型信息表）

| 字段名         | 数据类型     | 默认值 | 描述                                              |
| -------------- | ------------ | ------ | ------------------------------------------------- |
| DEVICETYPEID   | NUMBER(4)    | -      | 设备类型编号                                      |
| DEVICETYPENAME | VARCHAR2(32) | -      | 设备类型名称(如果需要MODALITY或类型名称的，统一提供这个字段)                                      |
| MODALITY       | VARCHAR2(8)  | -      | DICOM MODALITY                                    |
| ISAVAILABLE    | NUMBER(4)    | 1      | 是否可用，1：可用，0：不可用                      |
| OPERATORID     | VARCHAR2(32) | -      | 操作员编号                                        |
| OPERATETIME    | DATE         | -      | 操作时间                                          |
| REMARKINFO     | VARCHAR2(64) | -      | 备注信息                                          |
| METATYPE       | VARCHAR2(10) | -      | 设备类型元类型，例如用于输入模板CR/DR模板共用合并 |

## DEVICETABLE（设备明细表）

| 字段名          | 数据类型     | 默认值                     | 描述                                             |
| --------------- | ------------ | -------------------------- | ------------------------------------------------ |
| DEVICEID        | VARCHAR2(32) | -                          | 设备编号                                         |
| DEVICETYPEID    | NUMBER(4)    | -                          | 设备类型编号                                     |
| DEVICECODE      | VARCHAR2(16) | -                          | 设备代码                                         |
| DEVICENAME      | VARCHAR2(64) | -                          | 设备名称                                         |
| DEPARTMENTID    | VARCHAR2(32) | -                          | 所属部门编号                                     |
| ISAVAILABLE     | NUMBER(4)    | 1                          | 是否可用，1：可用，0：不可用                     |
| OPERATORID      | VARCHAR2(32) | -                          | 操作员编号                                       |
| OPERATETIME     | DATE         | -                          | 操作时间                                         |
| REMARKINFO      | VARCHAR2(64) | -                          | 备注                                             |
| ALLACOUNT       | NUMBER(16,2) | 0                          | 总价值                                           |
| CANUSEYEAR      | NUMBER(5)    | 1                          | 可用年数                                         |
| MAINTAINPERYEAR | NUMBER(16,2) | 0                          | 每年维护费用                                     |
| ADDINTIME       | VARCHAR2(6)  | to_char(sysdate, 'yyyymm') | 填加日期                                         |
| ROOMNAME        | VARCHAR2(16) | -                          | 所在房间名                                       |
| HOSPITALID      | VARCHAR2(32) | -                          | 简码-PACS30时用于影像号的生成-新版可用于区分院区 |
| CLEARDATE       | NUMBER(2)    | -                          | 保护天数：在近几天内的分诊数据不会被删除         |

## CHECKITEM（检查方法表）

| 字段名      | 数据类型     | 默认值 | 描述                         |
| ----------- | ------------ | ------ | ---------------------------- |
| CHECKITEMID | NUMBER       | -      | 检查项目编号                 |
| CHECKITEM   | VARCHAR2(50) | -      | 检查项目                     |
| ISAVAILABLE | NUMBER(4)    | 1      | 是否可用，1：可用，0：不可用 |
| OPERATORID  | VARCHAR2(32) | -      | 操作员编号                   |
| OPERATETIME | DATE         | -      | 操作时间                     |
| REMARKINFO  | VARCHAR2(64) | -      | 备注信息                     |

## STUDYPOSITIONINFO（检查部位信息表）

| 字段名            | 数据类型     | 默认值 | 描述                         |
| ----------------- | ------------ | ------ | ---------------------------- |
| STUDYPOSITIONID   | NUMBER       | -      | 检查部位编号                 |
| STUDYPOSITION     | VARCHAR2(64) | -      | 检查部位                     |
| STUDYPOSITIONCODE | VARCHAR2(16) | -      | 检查部位助记码               |
| ISAVAILABLE       | NUMBER(4)    | 1      | 是否可用，1：可用，0：不可用 |
| OPERATORID        | VARCHAR2(32) | -      | 操作员编号                   |
| OPERATETIME       | DATE         | -      | 操作时间                     |
| ORDERNO           | NUMBER       | -      | 排序，数字越大越靠前         |

## STUDYINFO（检查信息表）

| 字段名                 | 数据类型       | 默认值  | 描述                                                         |
| ---------------------- | -------------- | ------- | ------------------------------------------------------------ |
| CHECKSERIALNUM         | VARCHAR2(32)   | -       | 检查流水号                                                   |
| MAINNUM                | VARCHAR2(32)   | 0       | 如果主是多部位多条检查的模式，MainNum保存的是主检查流水号，否则：0 |
| DIAGRPTID              | VARCHAR2(32)   | -       | 报告编号                                                     |
| AGE                    | NUMBER(3)      | -       | 年龄                                                         |
| AGEUNIT                | VARCHAR2(4)    | -       | 年龄单位                                                     |
| FILENUM                | NUMBER(10)     | 0       | 图像文件数                                                   |
| PHOTOMAKER             | VARCHAR2(32)   | -       | 技师名称                                                     |
| DSTUDYUID              | VARCHAR2(64)   | -       | 检查唯一号                                                   |
| STUDYTIME              | DATE           | -       | 检查时间                                                     |
| CHKDEPTID              | VARCHAR2(32)   | -       | 检查部门编号                                                 |
| DEVICETYPEID           | NUMBER(4)      | -       | 设备类型编号                                                 |
| DEVICEID               | VARCHAR2(32)   | -       | 设备编号                                                     |
| OPERATORID             | VARCHAR2(32)   | -       | 操作员编号                                                   |
| OPERATETIME            | DATE           | -       | 操作时间                                                     |
| LOCKUSER               | VARCHAR2(32)   | -       | 锁定用户                                                     |
| DIAGID                 | VARCHAR2(80)   | -       | HIS申请单号                                                  |
| PREDIAGNOSE            | VARCHAR2(1000) | -       | 临床诊断                                                     |
| ABSTRACTHISTORY        | VARCHAR2(1000) | -       | 病史摘要                                                     |
| REQHOSPITAL            | VARCHAR2(64)   | -       | 申请医院                                                     |
| DEPARTMENTID           | VARCHAR2(32)   | -       | 申请科室编号                                                 |
| DOCTORCODE             | VARCHAR2(32)   | -       | 申请医生名称                                                 |
| SICKROOM               | VARCHAR2(64)   | -       | 病房                                                         |
| SICKBED                | VARCHAR2(64)   | -       | 病床                                                         |
| WEIGHT                 | NUMBER(5,1)    | -       | 体重                                                         |
| HEIGHT                 | NUMBER(5,1)    | -       | 身高                                                         |
| DICOMPATIENTNAME       | VARCHAR2(64)   | -       | DICOM患者名                                                  |
| SEPERATETIME           | DATE           | sysdate | 分诊时间                                                     |
| PRESTARTTIME           | DATE           | -       | 一线报告开始时间                                             |
| PREENDTIME             | DATE           | -       | 一线报告结束时间                                             |
| SECSTARTTIME           | DATE           | -       | 二线报告开始时间                                             |
| SECENDTIME             | DATE           | -       | 二线报告结束时间                                             |
| STUDYSCRIPTION         | VARCHAR2(1000) | -       | 检查项目，检查部位的组合描述串                               |
| BURN                   | VARCHAR2(4)    | 0       | 是否刻录，0:未刻录 1:已经刻录                                |
| STUDYID                | VARCHAR2(32)   | -       | 检查号                                                       |
| RELATIONCHECKSERIALNUM | VARCHAR2(32)   | -       | 相关诊断，手动关联 相关号                                    |
| FROSTSERIALNUM         | VARCHAR2(32)   | -       | 冰冻流水号                                                   |
| FROSTDIAGNOSE          | VARCHAR2(128)  | -       | 冰冻诊断                                                     |
| SJCL                   | VARCHAR2(128)  | -       | 送检材料                                                     |
| SAMPLE_COUNT           | VARCHAR2(16)   | 0       | 送检材料数量                                                 |
| SAMPLINGTIME           | DATE           | -       | 采样时间                                                     |
| ISMENOPAUSE            | VARCHAR2(1000) | -       | 是否绝经                                                     |
| LASTMENSESDATE         | DATE           | -       | 上次月经时间                                                 |
| HISCHECKITEM           | VARCHAR2(1000) | -       | HIS检查项目名（描述串）                                      |
| REMARK                 | VARCHAR2(1000) | -       | 备注（对应电子申请单的注意事项）                             |
| IFLOCK                 | NUMBER         | 0       | 是否加锁，0：正常，1：加锁                                   |
| IFEMERGENCY            | NUMBER         | 0       | 是否急诊，0：平诊，1：急诊                                   |
| SEPERATESTATUS         | NUMBER         | 0       | 分诊状态，0：设备分诊，1：直接分诊，2：补分诊                |
| STUDYSTATUS            | NUMBER         | 0       | 检查状态                                                     |
| IFMASCULINE            | NUMBER         | 0       | 是否阳性，0：未确定，1：阴性，2：阳性                        |
| TEMPSAVESTATE          | NUMBER         | 0       | 暂存状态，0：正常，1：暂存                                   |
| REFUSESTATE            | NUMBER         | 0       | 拒签状态，0：正常，1：拒签                                   |
| ISAVAILABLE            | NUMBER         | 0       | 删除标记，0：删除，1：正常                                   |
| PUTOUTFILM             | NUMBER         | 0       | 胶片发放状态 0：未发放，1：已打印，2：已发放                 |
| IFSCAN                 | NUMBER         | 0       | 是否扫描，0：未扫描，1：扫描                                 |
| IFCONSULTATION         | NUMBER         | 0       | 是否全科会诊，0：未会诊，1：会诊                             |
| ISBESPEAK              | NUMBER         | 0       | 是否预约，0：正常分诊，1：预约分诊                           |
| IFVALIDITY             | NUMBER         | 0       | 诊断是否正确，0：不正确，1：正确                             |
| IFSPECIAL              | NUMBER         | 0       | 是否特殊，0：不是特殊病例，1：是特殊病例                     |
| BACKUPFIELD2           | VARCHAR2(1000) | -       | 分诊系统备用字段2（分配报告医生名）                          |
| BACKUPFIELD3           | VARCHAR2(1000) | -       | 分诊系统备用字段3（金盘数据是图像数，新数据是分配审核医生名） |
| STUDYTYPE              | NUMBER         | 0       | 是否敏感检查，0：正常，1：敏感                               |
| FEETOTAL               | NUMBER(6,2)    | -       | 检查总费用                                                   |
| INVOICENO              | VARCHAR2(20)   | -       | 收据号                                                       |
| RIS_HOSTID             | NUMBER         | -1      | Ris主机ID，-1表示没有申请单主机关联                          |
| REQIMAGEPATH           | VARCHAR2(1000) | -       | 申请单图像路径                                               |
| QUEUENO                | NUMBER         | 0       | 队列号                                                       |
| BESPEAKTIME            | DATE           | -       | 预约时间                                                     |
| DEPARTMENTPATIENTID    | VARCHAR2(16)   | -       | 部门患者编号，即影像号                                       |
| PHOTOMAKERID           | VARCHAR2(32)   | -       | 检查技师ID                                                   |
| EXPMD5MARK             | VARCHAR2(32)   | -       | 被导库标记                                                   |
| REACHSTUDYTIME         | DATE           | -       | 患者到诊时间                                                 |
| ACCESSNUM              | VARCHAR2(32)   | -       | DICOM图像的ACCESSIONNUMBER                                   |
| ISMEDICALRECORD        | NUMBER         | -       | 是否是随访检查（病史档案），当此字段值为1时，该检查是随访检查 |
| ISHAVEHISREQUISITION   | NUMBER         | 0       | 是否有HIS电子申请单,0：没有，1：有                           |
| ORIGINCHECKSERIALNUM   | VARCHAR2(32)   | -       | 随访检查中，保存原始检查的CheckSerialNum —— 随访检查总是依附于一个正常检查 |
| EXPOSALCOUNT           | NUMBER(4)      | -       | 曝光次数                                                     |
| FILMCOUNT              | NUMBER(3)      | -       | 胶片数                                                       |
| FILMTYPEID             | NUMBER(3)      | -       | 胶片大小id （对应DICT_FILM_TYPE中的id）                      |
| REQIMAGEFILENAME       | VARCHAR2(100)  | -       | 申请单图像文件名称                                           |



## PATIENTINFO（患者信息表）

| 字段名           | 数据类型      | 默认值 | 描述                                 |
| ---------------- | ------------- | ------ | ------------------------------------ |
| CHECKSERIALNUM   | VARCHAR2(32)  | -      | 检查流水号                           |
| PATIENTNAME      | VARCHAR2(64)  | -      | 患者姓名                             |
| PATIENTSPELLNAME | VARCHAR2(64)  | -      | 患者拼音名称                         |
| BIRTHDAY         | DATE          | -      | 生日                                 |
| SEX              | VARCHAR2(4)   | -      | 性别                                 |
| IDNUMBER         | VARCHAR2(24)  | -      | 身份证号码                           |
| PHONENUMBER      | VARCHAR2(20)  | -      | 电话号码                             |
| NATIONALITY      | VARCHAR2(50)  | -      | 国籍                                 |
| FOLK             | VARCHAR2(50)  | -      | 民族                                 |
| ADDRESS          | VARCHAR2(200) | -      | 地址                                 |
| HISPATIENTTYPE   | NUMBER(2)     | -      | 本次检查的患者身份类型，门诊还是住院 |
| CLINICPATIENTID  | VARCHAR2(32)  | -      | 门诊患者编号                         |
| INFEEPATIENTID   | VARCHAR2(32)  | -      | 住院患者编号                         |
| ENETPATIENTID    | VARCHAR2(32)  | -      | 联网全局患者编号                     |
| SOCIETYID        | VARCHAR2(32)  | -      | 医保号                               |
| REMARK           | VARCHAR2(500) | -      | 对应分诊系统 BACKUPFIELD1            |
| OPERATORID       | VARCHAR2(32)  | -      | 操作员编号                           |
| OPERATETIME      | DATE          | -      | 操作时间                             |
| EMAIL            | VARCHAR2(255) | -      | 电子邮件                             |
| REQHOSPITALID    | VARCHAR2(32)  | -      | 申请医院ID号                         |



## STUDYAPPENDPOSITION（检查部位附加表）

| 字段名          | 数据类型     | 默认值 | 描述                         |
| --------------- | ------------ | ------ | ---------------------------- |
| CHECKSERIALNUM  | VARCHAR2(32) | -      | 检查流水号                   |
| STUDYPOSITIONID | NUMBER(4)    | -      | 检查部位编号                 |
| CHECKITEMID     | NUMBER       | -      | 检查项目编号                 |
| OPERATORID      | VARCHAR2(32) | -      | 操作员编号                   |
| OPERRATETIME    | DATE         | -      | 操作时间                     |
| ISAVAILABLE     | NUMBER       | 1      | 是否可用，1：可用，0：不可用 |



## PATIENTDIAGRPTINFO（报告表）

| 字段名              | 数据类型       | 默认值  | 描述                                           |
| ------------------- | -------------- | ------- | ---------------------------------------------- |
| DIAGRPTID           | VARCHAR2(32)   | -       | 报告编号                                       |
| REPORTDESCRIBE      | VARCHAR2(4000) | -       | 描述                                           |
| REPORTDIAGNOSE      | VARCHAR2(2000) | -       | 诊断                                           |
| REPORTADVICE        | VARCHAR2(1000) | -       | 建议                                           |
| DOCID1              | VARCHAR2(32)   | -       | 报告医生1                                      |
| DOCID2              | VARCHAR2(32)   | -       | 报告医生2                                      |
| DOCID3              | VARCHAR2(32)   | -       | 报告医生3                                      |
| DOCID4              | VARCHAR2(32)   | -       | 报告医生4                                      |
| DOCID5              | VARCHAR2(32)   | -       | 报告医生5                                      |
| DOCID6              | VARCHAR2(32)   | -       | 报告医生6                                      |
| FHDOCTOR            | VARCHAR2(32)   | -       | 辅助医生ID 可保存打字员                        |
| VERSION             | NUMBER         | 1       | 当前报告版本号                                 |
| OPERATORID          | VARCHAR2(32)   | -       | 用户编号                                       |
| OPERATETIME         | DATE           | sysdate | 最后提交报告时间                               |
| OTPLID              | VARCHAR2(32)   | -       | 输出模板                                       |
| VIEWUNDERMICROSCOPE | VARCHAR2(1000) | -       | 镜下见                                         |
| IDENTIFYKEY         | VARCHAR2(128)  | -       | 使用CA证书时，保存报告签名，防止报告被修改     |
| IFREPORTPRINT       | NUMBER         | 0       | 是否打印，0：未打印，1：已打印，2：已发放      |
| MEDIAID             | VARCHAR2(32)   | -       | RIS介质编号                                    |
| FILEPATH            | VARCHAR2(100)  | -       | 文件路径                                       |
| ISFILEINDEX         | NUMBER(1)      | -       | 是否注册的是文件索引，1：是，0：上传的完整文件 |
| SERIESUID           | VARCHAR2(32)   | -       | 序列UID IsFileIndex = 1 必填                   |
| INSTANCEUID         | VARCHAR2(64)   | -       | 报告快照UID IsFileIndex = 1 必填               |



## DICT_CRITICALVALUETYPE（危急值类型字典表）

| 字段名                | 数据类型     | 默认值 | 描述           |
| --------------------- | ------------ | ------ | -------------- |
| CRITICALVALUETYPEID   | VARCHAR2(32) | -      | 危急值类型ID   |
| CRITICALVALUETYPENAME | VARCHAR2(32) | -      | 危急值类型名称 |
| DEPARTMENTID          | VARCHAR2(32) | -      | 科室ID         |
| DEVICETYPEID          | NUMBER       | -      | 设备类型ID     |
| ORDERNO               | NUMBER       | -      | 排序           |
| OPERATOR              | VARCHAR2(32) | -      | 操作人         |
| OPERATETIME           | DATE         | -      | 操作时间       |



## DICT_CRITICALVALUE（危急值字典表）

| 字段名              | 数据类型     | 默认值 | 描述                   |
| ------------------- | ------------ | ------ | ---------------------- |
| CRITICALVALUEID     | VARCHAR2(32) | -      | 危急值ID               |
| CRITICALVALUENAME   | VARCHAR2(64) | -      | 危急值内容             |
| DEPARTMENTID        | VARCHAR2(32) | -      | 科室ID                 |
| DEVICETYPEID        | NUMBER       | -      | 设备类型ID             |
| CRITICALVALUETYPEID | VARCHAR2(32) | -      | 危急值类型ID           |
| ORDERNO             | NUMBER       | -      | 顺序                   |
| ISAVAILABLE         | NUMBER       | -      | 是否可用：1可用、0停用 |
| OPERATOR            | VARCHAR2(32) | -      | 操作人                 |
| OPERATETIME         | DATE         | -      | 操作时间               |



## DICT_CRITICALVALUESTATUS（危急值状态字典表）

| 字段名      | 数据类型     | 默认值 | 描述           |
| ----------- | ------------ | ------ | -------------- |
| STATUSID    | VARCHAR2(32) | -      | 危急值状态ID   |
| STATUSNAME  | VARCHAR2(64) | -      | 危急值状态名称 |
| OPERATOR    | VARCHAR2(32) | -      | 操作人         |
| OPERATETIME | DATE         | -      | 操作时间       |



## CRITICALVALUEINFO（危急值管理信息表）

| 字段名          | 数据类型       | 默认值 | 描述                                  |
| --------------- | -------------- | ------ | ------------------------------------- |
| CRITICALVALUEID | VARCHAR2(32)   | -      | 危急值记录id（主键）                  |
| CHECKSERIALNUM  | VARCHAR2(32)   | -      | 检查流水号                            |
| STATUS          | VARCHAR2(32)   | -      | 危急值状态：1已上报、2已接收、3已确认 |
| SENDER          | VARCHAR2(32)   | -      | 上报人                                |
| SENDERTEL       | VARCHAR2(32)   | -      | 上报人电话                            |
| CRITICALVALUE   | VARCHAR2(100)  | -      | 危急值                                |
| REMARK          | VARCHAR2(200)  | -      | 危急值描述                            |
| SENDTIME        | DATE           | -      | 上报时间                              |
| NURSE           | VARCHAR2(32)   | -      | 处理护士                              |
| NURSETEL        | VARCHAR2(32)   | -      | 护士电话                              |
| NURSEMSG        | VARCHAR2(1000) | -      | 护士处理记录                          |
| NURSETIME       | DATE           | -      | 护士处理时间                          |
| RECEIVER        | VARCHAR2(32)   | -      | 接收人                                |
| RECEIVERTEL     | VARCHAR2(32)   | -      | 接收人电话                            |
| RECEIVEMSG      | VARCHAR2(1000) | -      | 接收人意见                            |
| RECEIVETIME     | DATE           | -      | 接收时间                              |
| CONFIRMER       | VARCHAR2(32)   | -      | 确认人                                |
| CONFIRMERTEL    | VARCHAR2(32)   | -      | 确认人电话                            |
| CONFIRMMSG      | VARCHAR2(1000) | -      | 确认意见                              |
| CONFIRMTIME     | DATE           | -      | 确认时间                              |
| NOTEBOOK        | VARCHAR2(1000) | -      | 沟通记录                              |
| OPERATEINFO     | VARCHAR2(200)  | -      | 特殊操作记录                          |

