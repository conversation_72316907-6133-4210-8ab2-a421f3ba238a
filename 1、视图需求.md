视图名：**ANGYPrintList**

| 列名称               | 报告字段          | 描述                                                         |
| -------------------- | ----------------- | ------------------------------------------------------------ |
| pno                  | P 号              | 患者的唯一号，Patient ID，[varchar](20) NULL；P号是患者号，对应每个患者，和身份证号码一样，针对不同病人是唯一存在；需要在PACS与设备的worklist上体现，最终在图像上显示出来 |
| ano                  | A 号              | RIS本次检查记录的唯一编号，Accession Number，[varchar](20) NULL；A号是唯一号，对应每次检查，对应每次检查都必须唯一；需要在PACS与设备的worklist上体现，最终在图像上显示出来 |
| deviceno             | 影像号            | [varchar](20) NULL                                           |
| serialno             | 流水号            | [varchar](20) NULL                                           |
| examno               | 检查号            | [varchar](20) NULL                                           |
| Proaccount           | 职业号            | [varchar](20) NULL                                           |
| hzid                 | 患者编号          | [varchar](20) NULL                                           |
| ipno                 | 住院号            | [varchar](20) NULL                                           |
| medcardno            | 就诊卡号          | [varchar](20) NULL                                           |
| cardno               | 取片号            | [varchar](20) NULL，取片凭证                                 |
| clno                 | 门诊号            | [varchar](20) NULL                                           |
| regno                | 登记编号          | [varchar](20) NULL，RIS登记编号                              |
| patienttype          | 病人类别          | 门诊/急诊/住院/体检…                                         |
| tbpatient            | 结核病患者        | [varchar](20) NULL, 默认值为 NULL，1 表示结核病患者          |
| tel                  | 手机号码          | [varchar](20) NULL                                           |
| id                   | 身份证号码        | [varchar](20) NULL                                           |
| name                 | 患者姓名          | [varchar](20) NULL                                           |
| namepy               | 姓名拼音          | [varchar](20) NULL, 全拼                                     |
| sex                  | 性别              | 男/女，[varchar](10) NULL                                    |
| birthday             | 出生日期          | Date                                                         |
| age                  | 年龄              | [varchar](10) NULL，带单位（岁、月、天、小时等）             |
| bedno                | 床号              | [varchar](20) NULL                                           |
| dept                 | 申请科室          | [varchar](50) NULL                                           |
| Ward                 | 病区              | [varchar](50) NULL                                           |
| Symptoms             | 症状              | [varchar](50) NULL                                           |
| HP test              | HP试验            | [varchar](50) NULL                                           |
| Biopsysite           | 活检部位          | [varchar](50) NULL                                           |
| checkpart            | 检查部位          | [varchar](50) NULL                                           |
| Checkpartcount       | 检查部位收费数量  | [varchar](50) NULL                                           |
| Checkmeans           | 检查方法          | [varchar](100) NULL                                          |
| scanseq              | 扫描序列          | [varchar](50) NULL                                           |
| equipment            | 设备类型          | [varchar](50) NULL, Modality：CT、DR、MRI、CR、DSA           |
| device               | 检查仪器 AE Title | [varchar](20) NULL                                           |
| clindiag             | 临床诊断          | [varchar](5000) NULL                                         |
| feature              | 影像表现          | [varchar](5000) NULL                                         |
| diag                 | 影像诊断          | [varchar](5000) NULL                                         |
| Suggestion           | 建议              | [varchar](5000) NULL                                         |
| Pathologicdiag       | 病理诊断          | [varchar](5000) NULL                                         |
| Consultingdoc        | 会诊医生          | [varchar](20) NULL                                           |
| apydoc               | 申请医生          | [varchar](20) NULL                                           |
| rptdoc               | 报告医生          | [varchar](20) NULL                                           |
| auditdoc             | 审核医生          | [varchar](20) NULL                                           |
| apytime              | 申请日期          | Datetime 精确到秒                                            |
| regtime              | 登记时间          | Datetime RIS登记信息的时间，精确到秒                         |
| checktime            | 检查日期          | Datetime 精确到秒                                            |
| rpttime              | 报告日期          | Datetime 精确到秒                                            |
| audittime            | 审核日期          | Datetime 精确到秒                                            |
| status               | 报告状态          | [varchar](20) NULL 登记 10、检查 20、已写未审核 30、临时报告 35、已写已审核 40、暂存 45、已打印 50、已审核撤回 55、临时报告撤回 60…… |
| reportpath           | PDF 报告路径      | 1) 通过FTP/http等方式提供PDF报告，该列为PDF报告的路径；2) 不建议使用共享；3) 根据报告状态，需要即时生成或替换pdf文件，以免自助系统获取报告失败 |
| Filmcharge           | 胶片收费          | [varchar](20) NULL, 默认值为 null。病人是否支付胶片费用，0 表示未缴费，1 表示已缴费 |
| imagefilepath        | Dicom 图像路径    | DICOM数据接口主通道，FTP方式获取Dicom数据，此为Dicom影像的路径 |
| studyinstanceuid     | 检查记录唯一编号  | DICOM数据接口辅助通道，检查记录唯一标识，可标识PACS唯一dicom数据（StudyInstanceUid） |
| Dicomcount           | DICOM 图像数量    | [varchar](50) NULL, 该患者本次检查在PACS存储的DICOM图像数量  |
| DICOMArchivingStatus | DICOM 归档状态    | [varchar](20) NULL 未归档 10、开始归档 20、归档完成 30、归档文件更新 35 |